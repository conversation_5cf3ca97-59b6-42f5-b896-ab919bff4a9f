<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>HAP</Name>
  <Location>Engine\Source\ThirdParty\HAPMedia\HAPLib</Location>
  <Function>As a video codec within our windows media foundation framework used through wmf media.</Function>
  <Eula>https://github.com/Vidvox/hap/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/hap_License.txt</LicenseFolder>
</TpsData>