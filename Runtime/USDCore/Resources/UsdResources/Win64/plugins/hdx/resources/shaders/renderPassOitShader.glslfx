-- glslfx version 0.1

//
// Copyright 2019 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdx/shaders/renderPassOitShader.glslfx

#import $TOOLS/hdSt/shaders/renderPass.glslfx
#import $TOOLS/hdx/shaders/renderPass.glslfx
#import $TOOLS/hdx/shaders/selection.glslfx

-- configuration
{
    "techniques": {
        "default": {
            "vertexShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "postTessControlShader" : {
                "source": [ "RenderPass.Camera" ]
            },
            "postTessVertexShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "tessControlShader" : {
                "source": [ "RenderPass.Camera" ]
            },
            "tessEvalShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "geometryShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.ApplyClipPlanes" ]
            },
            "fragmentShader" : {
                "source": [ "RenderPass.Camera",
                            "RenderPass.CameraFS",
                            "Selection.DecodeUtils",
                            "Selection.ComputeColor",
                            "RenderPass.ApplyColorOverrides",
                            "HdxRenderPass.WriteOitLayersToBufferCommon",
                            "HdxRenderPass.WriteOitLayersToBufferTranslucent" ]
            }
        }
    }
}
