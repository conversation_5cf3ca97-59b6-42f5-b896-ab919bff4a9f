// Fill out your copyright notice in the Description page of Project Settings.


#include "AbilitySystem/Ability/RogueGameplayAbility.h"

#include "RogueTypes.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAbilitySystemLibrary.h"
#include "AbilitySystem/RogueAttributeSet.h"
#include "Character/RogueCharacterBase.h"

URogueGameplayAbility::URogueGameplayAbility()
{
	NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::LocalPredicted;
	InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
}

void URogueGameplayAbility::ApplyDamageInstantToActor(AActor* TargetActor)
{
	FRogueDamageParams DamageParams;

	
	ARogueCharacterBase* RogueCharacter = Cast<ARogueCharacterBase>(GetAvatarActorFromActorInfo());
	const float DamageMultiplier = RogueCharacter->GetRogueAS()->GetDamage();

	URogueAbilitySystemLibrary::MakeDamageParams(Damage * DamageMultiplier, TargetActor,
	                                             GetAvatarActorFromActorInfo(), DamageParams);

	URogueAbilitySystemLibrary::ApplyDamageInstant(DamageParams);
}

void URogueGameplayAbility::ActivateAbility(const FGameplayAbilitySpecHandle Handle,
                                            const FGameplayAbilityActorInfo* ActorInfo,
                                            const FGameplayAbilityActivationInfo ActivationInfo,
                                            const FGameplayEventData* TriggerEventData)
{
	Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
}

float URogueGameplayAbility::GetAttackSpeed() const
{
	ARogueCharacterBase* RogueCharacter = Cast<ARogueCharacterBase>(GetAvatarActorFromActorInfo());
	return RogueCharacter->GetRogueAS()->GetAttackSpeed();
}

ARogueCharacterBase* URogueGameplayAbility::GetRogueCharacter()
{
	ARogueCharacterBase* ARogueCharacter = CastChecked<ARogueCharacterBase>(GetAvatarActorFromActorInfo());
	return ARogueCharacter;
}

URogueAbilitySystemComponent* URogueGameplayAbility::GetRogueASC()
{
	URogueAbilitySystemComponent* RogueASC = CastChecked<URogueAbilitySystemComponent>(
		GetAbilitySystemComponentFromActorInfo());
	return RogueASC;
}

AController* URogueGameplayAbility::GetControllerFromActorInfo()
{
	if (CurrentActorInfo)
	{
		if (AController* PC = CurrentActorInfo->PlayerController.Get())
		{
			return PC;
		}

		// Look for a player controller or pawn in the owner chain.
		AActor* TestActor = CurrentActorInfo->OwnerActor.Get();
		while (TestActor)
		{
			if (AController* C = Cast<AController>(TestActor))
			{
				return C;
			}

			if (APawn* Pawn = Cast<APawn>(TestActor))
			{
				return Pawn->GetController();
			}

			TestActor = TestActor->GetOwner();
		}
	}

	return nullptr;
}
