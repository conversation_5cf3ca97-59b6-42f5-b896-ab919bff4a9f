// Fill out your copyright notice in the Description page of Project Settings.


#include "AbilitySystem/Ability/RogueRangedAbility.h"

#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAbilitySystemLibrary.h"
#include "Character/RogueCharacterBase.h"
#include "Interaction/CombatInterface.h"
#include "Kismet/KismetSystemLibrary.h"

// RogueRangedAbility.cpp

void URogueRangedAbility::ActivateAbility(
	const FGameplayAbilitySpecHandle Handle,
	const FGameplayAbilityActorInfo* ActorInfo,
	const FGameplayAbilityActivationInfo ActivationInfo,
	const FGameplayEventData* TriggerEventData)
{
	Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
}

void URogueRangedAbility::EndAbility(const FGameplayAbilitySpecHandle Handle,
	const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo,
	bool bReplicateEndAbility, bool bWasCancelled)
{
	Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

void URogueRangedAbility::StartRangedWeaponTargeting(const FVector& TraceEndOverride)
{
    if (!CurrentActorInfo->IsLocallyControlled())
    {
        return;      // 只在本地玩家执行
    }

	// 1) 绑定一次性的 TargetData 回调
	UAbilitySystemComponent* ASC = CurrentActorInfo->AbilitySystemComponent.Get();
	check(ASC);
	OnTargetDataReadyCallbackDelegateHandle =
		ASC->AbilityTargetDataSetDelegate(CurrentSpecHandle, CurrentActivationInfo.GetActivationPredictionKey())
		   .AddUObject(this, &URogueRangedAbility::OnTargetDataReadyCallback);

    /* ======= 你的相机 → 击中点算法（保持不变） ======= */
    ARogueCharacterBase* RC = GetRogueCharacter();
    FVector TraceStart      = ICombatInterface::Execute_GetCombatSocket(RC, 0);
    FVector CameraHitPoint  = TraceEndOverride;

    if (APlayerController* PC = Cast<APlayerController>(GetControllerFromActorInfo()))
    {
        int32 VX, VY;  PC->GetViewportSize(VX, VY);
        FVector CamLoc, CamDir;
        PC->DeprojectScreenPositionToWorld(VX * 0.5f, VY * 0.5f, CamLoc, CamDir);

        FHitResult CamHit;
        const float CamRange = 10000.f;
        GetWorld()->LineTraceSingleByChannel(CamHit, CamLoc, CamLoc + CamDir * CamRange, ECC_Visibility);
        CameraHitPoint = CamHit.bBlockingHit ? CamHit.ImpactPoint : (CamLoc + CamDir * CamRange);
    }

    FVector BulletDir  = (CameraHitPoint - TraceStart).GetSafeNormal();
    const float Range  = 10000.f;
    FVector FinalEnd   = TraceStart + BulletDir * Range;

    FHitResult Hit;
    GetWorld()->LineTraceSingleByChannel(Hit, TraceStart, FinalEnd, ECC_Visibility);

    /* ======= 给蓝图播本地击中特效 ======= */
    if (Hit.bBlockingHit)
    {
        OnShootHit(Hit);
    }

    /* ======= 封装 TargetData ======= */
    FGameplayAbilityTargetDataHandle DataHandle;
    if (Hit.bBlockingHit)
    {
        auto* SingleData   = new FGameplayAbilityTargetData_SingleTargetHit(Hit);
        DataHandle.Add(SingleData);
    }
    else
    {
        // 未命中也要告诉服务器射了空弹（可选）
        auto* MissData = new FGameplayAbilityTargetData_LocationInfo();
        DataHandle.Add(MissData);
    }

    /* ======= 通过 GAS 内置 RPC 发送给服务器 ======= */
    FScopedPredictionWindow ScopedPred(ASC, CurrentActivationInfo.GetActivationPredictionKey());

    ASC->CallServerSetReplicatedTargetData(
        CurrentSpecHandle,
        CurrentActivationInfo.GetActivationPredictionKey(),
        DataHandle,
        /*ApplicationTag=*/FGameplayTag(),   // 如果想用 GameplayCue，可在此传 Tag
        ASC->ScopedPredictionKey);

	ASC->ConsumeClientReplicatedTargetData(CurrentSpecHandle, CurrentActivationInfo.GetActivationPredictionKey());

}

void URogueRangedAbility::OnTargetDataReadyCallback(
	const FGameplayAbilityTargetDataHandle& InData,
	FGameplayTag ApplicationTag)
{
	GEngine->AddOnScreenDebugMessage(-1, 1.0f, FColor::Yellow
								 , FString::Printf(
									 TEXT("OnTargetDataReadyCallback in %s:"),
									 CurrentActorInfo->IsNetAuthority() ? TEXT("Server") : TEXT("Client")));
	
	UAbilitySystemComponent* ASC = CurrentActorInfo->AbilitySystemComponent.Get();
	check(ASC);

	// 只要收到数据（本地或服务器）都走下面逻辑
	// 本例主要关心服务器端
	const bool bIsServer = CurrentActorInfo->IsNetAuthority();

	/* ======= 服务器：权威处理 ======= */
	if (bIsServer)
	{
		OnTargetDataReady(InData);
	}

	/* ======= 回收 / 清理 ======= */
	ASC->ConsumeClientReplicatedTargetData(CurrentSpecHandle,
										   CurrentActivationInfo.GetActivationPredictionKey());
	ASC->AbilityTargetDataSetDelegate(CurrentSpecHandle,
									  CurrentActivationInfo.GetActivationPredictionKey())
	   .Remove(OnTargetDataReadyCallbackDelegateHandle);
}
