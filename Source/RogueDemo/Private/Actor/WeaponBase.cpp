// Fill out your copyright notice in the Description page of Project Settings.


#include "Actor/WeaponBase.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "RogueLogChannel.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "Character/RogueCharacterBase.h"
#include "Components/BoxComponent.h"
#include "Game/RogueGameplayTags.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Net/UnrealNetwork.h"

AWeaponBase::AWeaponBase()
{
	WeaponRoot = CreateDefaultSubobject<USceneComponent>("Root");
	SetRootComponent(WeaponRoot);
	bReplicates = true;

	WeaponMesh = CreateDefaultSubobject<UStaticMeshComponent>("WeaponMesh");
	WeaponMesh->SetupAttachment(WeaponRoot);
	WeaponMesh->SetCollisionEnabled(ECollisionEnabled::Type::NoCollision);

	WeaponSkeletalMesh = CreateDefaultSubobject<USkeletalMeshComponent>("WeaponSkeletalMesh");
	WeaponSkeletalMesh->SetupAttachment(WeaponRoot);
	WeaponMesh->SetCollisionEnabled(ECollisionEnabled::Type::NoCollision);

	WeaponCollision = CreateDefaultSubobject<UBoxComponent>("WeaponCollision");
	WeaponCollision->SetupAttachment(WeaponRoot);
	WeaponCollision->SetCollisionEnabled(ECollisionEnabled::Type::QueryOnly);
	WeaponCollision->SetCollisionResponseToAllChannels(ECR_Overlap);
	// WeaponCollision->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
}

void AWeaponBase::EnableCollision()
{
	if (!WeaponCollision || bEnabledCollision) return;
	bEnabledCollision = true;

	// 确保有效
	if (FBodyInstance* BI = WeaponCollision->GetBodyInstance();
		!BI || !BI->IsValidBodyInstance())
	{
		WeaponCollision->RecreatePhysicsState();
	}

	WeaponCollision->SetGenerateOverlapEvents(true);
	WeaponCollision->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	WeaponCollision->UpdateOverlaps(); // 刷新但不强制通知
}

void AWeaponBase::DisableCollision()
{
	if (!WeaponCollision || !bEnabledCollision) return;
	bEnabledCollision = false;

	// 先结束所有重叠，清理双方缓存，避免 ServerMove 收尾再次校验
	WeaponCollision->SetGenerateOverlapEvents(false);
	WeaponCollision->SetCollisionEnabled(ECollisionEnabled::NoCollision);
}

FVector AWeaponBase::GetCombatSocket() const
{
	FVector CombatVector = WeaponSkeletalMesh->GetSocketLocation(FName("CombatSocket"));

	return CombatVector;
}

void AWeaponBase::BeginPlay()
{
	Super::BeginPlay();

	WeaponCollision->OnComponentBeginOverlap.AddDynamic(this, &ThisClass::OnBeginOverlap);
	WeaponCollision->OnComponentEndOverlap.AddDynamic(this, &ThisClass::OnEndOverlap);

	WeaponCollision->SetCollisionEnabled(ECollisionEnabled::Type::NoCollision);
}

void AWeaponBase::GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	DOREPLIFETIME(AWeaponBase, AttachSocketName);
	DOREPLIFETIME(AWeaponBase, AttachRelativeTransform);
}

void AWeaponBase::OnRep_AttachData()
{
	GEngine->AddOnScreenDebugMessage(-1, 1.0f, FColor::Yellow
	                                 , FString::Printf(
		                                 TEXT("OnRep_AttachData in %s:"),
		                                 HasAuthority() ? TEXT("Server") : TEXT("Client")));
	AttachToComponent(GetOwnerCharacter()->GetMesh(), FAttachmentTransformRules::KeepRelativeTransform,
	                  AttachSocketName);
	SetActorRelativeTransform(AttachRelativeTransform);
}

ACharacter* AWeaponBase::GetOwnerCharacter() const
{
	return Cast<ACharacter>(GetOwner());
}

void AWeaponBase::OnBeginOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp,
                                 int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	FGameplayEventData Payload;
	Payload.EventTag = RogueGameplayTags::GameplayEvent_Attack;
	Payload.Instigator = GetOwner();
	Payload.Target = Other;

	if (Other == GetOwner()) return;

	ARogueCharacterBase* RogueCharacterBase = Cast<ARogueCharacterBase>(GetOwner());

	if (HasAuthority())
		RogueCharacterBase->GetRogueASC()->SendGameplayEventToSelf(RogueGameplayTags::GameplayEvent_Attack, Payload);
}

void AWeaponBase::OnEndOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp,
                               int32 OtherBodyIndex)
{
}
