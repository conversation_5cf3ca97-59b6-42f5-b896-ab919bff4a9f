// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "EnhancedInputComponent.h"
#include "GameplayTagContainer.h"
#include "RogueInputConfig.h"
#include "RogueInputComponent.generated.h"

class URogueInputConfig;
struct FRogueInputAction;
/**
 * 
 */
UCLASS()
class ROGUEDEMO_API URogueInputComponent : public UEnhancedInputComponent
{
	GENERATED_BODY()

public:
	template <class UserClass, typename FuncType>
	void BindNativeAction(URogueInputConfig* RogueInputConfig, FGameplayTag InputTag, ETriggerEvent TriggerEvent,
	                      UserClass* Object,
	                      FuncType Func, bool bLogIfNotFound = true);

	template <class UserClass, typename FuncType>
	void BindAbilityActions(URogueInputConfig* RogueInputConfig,
	                                              UserClass* Object,
	                                              FuncType PressedFunc,
	                                              FuncType HeldFunc,
	                                              FuncType ReleasedFunc,
	                                              TArray<uint32>& OutBoundHandles);
};

template <class UserClass, typename FuncType>
void URogueInputComponent::BindNativeAction(URogueInputConfig* RogueInputConfig, FGameplayTag InputTag,
                                            ETriggerEvent TriggerEvent,
                                            UserClass* Object, FuncType Func, bool bLogIfNotFound)
{
	check(RogueInputConfig);
	if (UInputAction* InputAction = RogueInputConfig->FindNativeInputActionForTag(InputTag, bLogIfNotFound))
	{
		BindAction(InputAction, TriggerEvent, Object, Func);
	}
}

template <class UserClass, typename FuncType>
void URogueInputComponent::BindAbilityActions(URogueInputConfig* RogueInputConfig,
                                              UserClass* Object,
                                              FuncType PressedFunc,
                                              FuncType HeldFunc,
                                              FuncType ReleasedFunc,
                                              TArray<uint32>& OutBoundHandles)
{
	check(RogueInputConfig)
	for (const FRogueInputAction& RogueInputAction : RogueInputConfig->GetAbilityInputActions())
	{
		if (RogueInputAction.InputAction && RogueInputAction.InputTag.IsValid())
		{
			if (PressedFunc)
				OutBoundHandles.Add(BindAction(RogueInputAction.InputAction, ETriggerEvent::Started, Object,
											   PressedFunc, RogueInputAction.InputTag).GetHandle());
			if (HeldFunc)
				OutBoundHandles.Add(BindAction(RogueInputAction.InputAction, ETriggerEvent::Triggered, Object,
				                               HeldFunc, RogueInputAction.InputTag).GetHandle());
			if (ReleasedFunc)
				OutBoundHandles.Add(BindAction(RogueInputAction.InputAction, ETriggerEvent::Completed, Object,
				                               ReleasedFunc, RogueInputAction.InputTag).GetHandle());

		}
	}
}
