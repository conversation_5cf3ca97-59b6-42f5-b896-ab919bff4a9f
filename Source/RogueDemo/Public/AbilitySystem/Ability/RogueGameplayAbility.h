// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Abilities/GameplayAbility.h"
#include "RogueLogChannel.h"
#include "RogueGameplayAbility.generated.h"

class URogueAbilitySystemComponent;
class ARogueCharacterBase;

UENUM()
enum class ERogueAbilityActivationPolicy : uint8
{
	// Try to activate the ability when the input is triggered.
	OnInputTriggered,

	// Continually try to activate the ability while the input is active.
	WhileInputActive,

	// Try to activate the ability when an avatar is assigned.
	OnSpawn
};

/**
 * 
 */
UCLASS()
class ROGUEDEMO_API URogueGameplayAbility : public UGameplayAbility
{
	GENERATED_BODY()

public:
	URogueGameplayAbility();

	/** ~Damage **/
	UFUNCTION(BlueprintCallable, Category = "Ability|Damage")
	void ApplyDamageInstantToActor(AActor* TargetActor);

	/** ~Getters **/
	FORCEINLINE ERogueAbilityActivationPolicy GetActivationPolicy() const { return ActivationPolicy; }

protected:
	virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo,
	                             const FGameplayAbilityActivationInfo ActivationInfo,
	                             const FGameplayEventData* TriggerEventData) override;

	UPROPERTY(BlueprintReadOnly, EditDefaultsOnly, Category = "Ability|Damage")
	float Damage;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Ability|Damage")
	float GetAttackSpeed() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Ability|Utils")
	ARogueCharacterBase* GetRogueCharacter();
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Ability|Utils")
	URogueAbilitySystemComponent* GetRogueASC();
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Ability|Utils")
	AController* GetControllerFromActorInfo();
private:
	UPROPERTY(EditDefaultsOnly, Category = "Rogue Ability System")
	ERogueAbilityActivationPolicy ActivationPolicy;
};
