// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystem/Ability/RogueGameplayAbility.h"
#include "RogueRangedAbility.generated.h"

/**
 * 
 */
// RogueRangedAbility.h

UCLASS()
class ROGUEDEMO_API URogueRangedAbility : public URogueGameplayAbility
{
	GENERATED_BODY()

protected:
	virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo,
	                             const FGameplayAbilityActivationInfo ActivationInfo,
	                             const FGameplayEventData* TriggerEventData) override;
	virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;

	/** 本地做 HitScan，随后给服务器发 TargetData */
	UFUNCTION(BlueprintCallable, Category="Ability|Damage")
	void StartRangedWeaponTargeting(const FVector& TraceEndOverride = FVector::ZeroVector);

	/** 服务器回调：服务器或客户端都可能触发 */
	UFUNCTION()
	void OnTargetDataReadyCallback(const FGameplayAbilityTargetDataHandle& Data, FGameplayTag ApplicationTag);

	/** BlueprintHook：用于播放击中特效等客户端视觉 */
	UFUNCTION(BlueprintImplementableEvent, Category="Damage")
	void OnShootHit(const FHitResult& HitResult);

	/** BlueprintHook：服务器端确认命中后可触发（可选） */
	UFUNCTION(BlueprintImplementableEvent, Category="Damage")
	void OnTargetDataReady(const FGameplayAbilityTargetDataHandle& Data);

private:
	FDelegateHandle OnTargetDataReadyCallbackDelegateHandle;
};
