// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Actor/RogueActorBase.h"
#include "WeaponBase.generated.h"

class UNiagaraSystem;
struct FGameplayEventData;
class UBoxComponent;
/**
 * 
 */
UCLASS()
class ROGUEDEMO_API AWeaponBase : public ARogueActorBase
{
	GENERATED_BODY()

public:
	AWeaponBase();

	void EnableCollision();
	void DisableCollision();

	FVector GetCombatSocket() const;

	/** 要挂到哪个 Socket */
	UPROPERTY(ReplicatedUsing=OnRep_AttachData)
	FName AttachSocketName;

	/** 相对那个 Socket 的偏移 */
	UPROPERTY(ReplicatedUsing=OnRep_AttachData)
	FTransform AttachRelativeTransform;
protected:
	/** ~begin AActor interface */
	virtual void BeginPlay() override;
	virtual void GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const override;
	/** ~end AActor interface */

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	TObjectPtr<UStaticMeshComponent> WeaponMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	TObjectPtr<USkeletalMeshComponent> WeaponSkeletalMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	TObjectPtr<USceneComponent> WeaponRoot;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	TObjectPtr<UBoxComponent> WeaponCollision;

	UFUNCTION()
	void OnRep_AttachData();

	ACharacter* GetOwnerCharacter() const;	
private:
	UFUNCTION()

	virtual void OnBeginOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp,
	                            int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	UFUNCTION()
	virtual void OnEndOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp,
	                          int32 OtherBodyIndex);

	bool bEnabledCollision = false;
};
